<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gamma AI - Revolutionizing Content Creation</title>
  <style>
      * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
      }
      
      body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.6;
          color: #1a1a1a;
          background: #fff;
          overflow-x: hidden;
      }
      
      .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 20px;
      }
      
      header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #f5576c 100%);
          color: white;
          padding: 5rem 0;
          text-align: center;
          position: relative;
          overflow: hidden;
      }
      
      header::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: 
              radial-gradient(circle at 20% 50%, rgba(255,255,255,0.2) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255,255,255,0.15) 0%, transparent 50%),
              radial-gradient(circle at 40% 80%, rgba(255,255,255,0.1) 0%, transparent 50%);
          animation: float 8s ease-in-out infinite;
      }
      
      @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(5deg); }
      }
      
      .header-content {
          position: relative;
          z-index: 2;
      }
      
      .logo {
          font-size: 4.5rem;
          font-weight: 900;
          margin-bottom: 1rem;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 1rem;
          animation: slideInDown 1s ease;
          letter-spacing: -2px;
      }
      
      .logo-icon {
          width: 90px;
          height: 90px;
          background: rgba(255, 255, 255, 0.25);
          border-radius: 25px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 3rem;
          backdrop-filter: blur(15px);
          border: 2px solid rgba(255, 255, 255, 0.3);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      }
      
      .tagline {
          font-size: 1.8rem;
          opacity: 0.95;
          max-width: 900px;
          margin: 0 auto 1.5rem;
          animation: slideInUp 1s ease 0.3s both;
          font-weight: 300;
          line-height: 1.4;
      }
      
      .subtitle {
          font-size: 1.2rem;
          opacity: 0.9;
          margin-bottom: 3rem;
          animation: fadeIn 1s ease 0.6s both;
          max-width: 700px;
          margin-left: auto;
          margin-right: auto;
      }
      
      .hero-stats {
          display: flex;
          justify-content: center;
          gap: 3rem;
          margin-top: 2rem;
          flex-wrap: wrap;
          animation: fadeIn 1s ease 0.9s both;
      }
      
      .stat {
          text-align: center;
          background: rgba(255, 255, 255, 0.1);
          padding: 1.5rem 2rem;
          border-radius: 15px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
      }
      
      .stat-number {
          font-size: 2.2rem;
          font-weight: bold;
          display: block;
          margin-bottom: 0.5rem;
      }
      
      .stat-label {
          font-size: 1rem;
          opacity: 0.9;
      }
      
      .cta-section {
          background: #f8f9fa;
          padding: 4rem 0;
          text-align: center;
      }
      
      .cta-title {
          font-size: 2.5rem;
          margin-bottom: 1rem;
          color: #1a1a1a;
          font-weight: 700;
      }
      
      .cta-subtitle {
          font-size: 1.3rem;
          color: #666;
          margin-bottom: 3rem;
      }
      
      .cta-buttons {
          display: flex;
          gap: 2rem;
          justify-content: center;
          flex-wrap: wrap;
      }
      
      .cta-button {
          display: inline-flex;
          align-items: center;
          gap: 0.8rem;
          padding: 18px 36px;
          font-size: 1.2rem;
          font-weight: 600;
          text-decoration: none;
          border-radius: 50px;
          transition: all 0.3s ease;
          border: 2px solid transparent;
          position: relative;
          overflow: hidden;
      }
      
      .cta-button::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
          transition: left 0.5s ease;
      }
      
      .cta-button:hover::before {
          left: 100%;
      }
      
      .cta-primary {
          background: linear-gradient(135deg, #667eea, #764ba2);
          color: white;
      }
      
      .cta-primary:hover {
          transform: translateY(-3px);
          box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
      }
      
      .cta-secondary {
          background: white;
          color: #667eea;
          border-color: #667eea;
      }
      
      .cta-secondary:hover {
          background: #f8f9ff;
          transform: translateY(-3px);
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      }
      
      .video-section {
          padding: 6rem 0;
          background: white;
      }
      
      .video-container {
          max-width: 1000px;
          margin: 0 auto;
          text-align: center;
      }
      
      .section-title {
          font-size: 3rem;
          margin-bottom: 1rem;
          color: #1a1a1a;
          font-weight: 800;
      }
      
      .section-subtitle {
          font-size: 1.4rem;
          color: #666;
          margin-bottom: 3rem;
          font-weight: 300;
      }
      
      .presenter-intro {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          color: white;
          padding: 2.5rem;
          border-radius: 20px;
          margin: 2rem 0;
          text-align: center;
          position: relative;
          overflow: hidden;
      }
      
      .presenter-intro::before {
          content: '';
          position: absolute;
          top: -50%;
          right: -50%;
          width: 200%;
          height: 200%;
          background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
          animation: rotate 15s linear infinite;
      }
      
      @keyframes rotate {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
      }
      
      .presenter-intro h3 {
          font-size: 1.6rem;
          margin-bottom: 1rem;
          position: relative;
          z-index: 2;
      }
      
      .presenter-intro p {
          position: relative;
          z-index: 2;
          font-size: 1.1rem;
      }
      
      .video-wrapper {
          position: relative;
          width: 100%;
          padding-bottom: 56.25%;
          margin: 3rem 0;
          border-radius: 25px;
          overflow: hidden;
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
          transition: transform 0.3s ease;
      }
      
      .video-wrapper:hover {
          transform: scale(1.02);
      }
      
      .video-wrapper iframe {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
      }
      
      .features-section {
          padding: 6rem 0;
          background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      }
      
      .features-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
          gap: 3rem;
          margin-top: 4rem;
      }
      
      .feature-card {
          background: white;
          padding: 3.5rem 3rem;
          border-radius: 25px;
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
          transition: all 0.4s ease;
          cursor: pointer;
          border: 2px solid transparent;
          position: relative;
          overflow: hidden;
      }
      
      .feature-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
          transition: left 0.6s ease;
      }
      
      .feature-card:hover::before {
          left: 100%;
      }
      
      .feature-card:hover {
          transform: translateY(-12px);
          box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
          border-color: #667eea;
      }
      
      .feature-number {
          display: inline-block;
          background: linear-gradient(135deg, #667eea, #764ba2);
          color: white;
          width: 50px;
          height: 50px;
          border-radius: 50%;
          font-size: 1.5rem;
          font-weight: bold;
          text-align: center;
          line-height: 50px;
          margin-bottom: 2rem;
      }
      
      .feature-title {
          font-size: 1.8rem;
          margin-bottom: 1.5rem;
          color: #1a1a1a;
          font-weight: 700;
      }
      
      .feature-description {
          color: #666;
          line-height: 1.8;
          font-size: 1.1rem;
          margin-bottom: 2rem;
      }
      
      .feature-list {
          list-style: none;
      }
      
      .feature-list li {
          padding: 0.8rem 0;
          position: relative;
          padding-left: 2rem;
          color: #555;
      }
      
      .feature-list li::before {
          content: '✨';
          position: absolute;
          left: 0;
          font-size: 1.2rem;
      }
      
      .demo-section {
          padding: 6rem 0;
          background: white;
      }
      
      .demo-steps {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 2rem;
          margin-top: 4rem;
      }
      
      .demo-step {
          background: #f8f9fa;
          padding: 2.5rem;
          border-radius: 20px;
          text-align: center;
          transition: all 0.3s ease;
          border: 2px solid transparent;
      }
      
      .demo-step:hover {
          transform: translateY(-5px);
          border-color: #667eea;
          background: white;
          box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
      }
      
      .demo-step-number {
          display: inline-block;
          background: linear-gradient(135deg, #f093fb, #f5576c);
          color: white;
          width: 60px;
          height: 60px;
          border-radius: 50%;
          font-size: 1.8rem;
          font-weight: bold;
          text-align: center;
          line-height: 60px;
          margin-bottom: 1.5rem;
      }
      
      .demo-step-title {
          font-size: 1.4rem;
          margin-bottom: 1rem;
          color: #1a1a1a;
          font-weight: 600;
      }
      
      .demo-step-description {
          color: #666;
          line-height: 1.6;
      }
      
      .capabilities-section {
          padding: 6rem 0;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
      }
      
      .capabilities-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 2rem;
          margin-top: 4rem;
      }
      
      .capability-card {
          background: rgba(255, 255, 255, 0.1);
          padding: 2.5rem;
          border-radius: 20px;
          text-align: center;
          transition: all 0.3s ease;
          backdrop-filter: blur(15px);
          border: 1px solid rgba(255, 255, 255, 0.2);
      }
      
      .capability-card:hover {
          transform: translateY(-8px);
          background: rgba(255, 255, 255, 0.15);
      }
      
      .capability-icon {
          font-size: 3.5rem;
          margin-bottom: 1.5rem;
          display: block;
      }
      
      .capability-title {
          font-size: 1.4rem;
          margin-bottom: 1rem;
          font-weight: 600;
      }
      
      .capability-description {
          font-size: 1rem;
          line-height: 1.6;
          opacity: 0.9;
      }
      
      .interactive-demo {
          padding: 6rem 0;
          background: #f8f9fa;
      }
      
      .demo-interface {
          max-width: 800px;
          margin: 0 auto;
          background: white;
          padding: 4rem;
          border-radius: 25px;
          box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
      }
      
      .demo-input-group {
          margin-bottom: 2rem;
      }
      
      .demo-label {
          display: block;
          margin-bottom: 1rem;
          font-weight: 600;
          color: #1a1a1a;
      }
      
      .demo-input {
          width: 100%;
          padding: 1.5rem;
          border: 2px solid #e0e0e0;
          border-radius: 15px;
          font-size: 1.1rem;
          transition: border-color 0.3s ease;
      }
      
      .demo-input:focus {
          outline: none;
          border-color: #667eea;
      }
      
      .demo-select {
          width: 100%;
          padding: 1.5rem;
          border: 2px solid #e0e0e0;
          border-radius: 15px;
          font-size: 1.1rem;
          background: white;
      }
      
      .demo-button {
          background: linear-gradient(135deg, #667eea, #764ba2);
          color: white;
          border: none;
          padding: 18px 40px;
          border-radius: 50px;
          font-weight: 600;
          cursor: pointer;
          font-size: 1.2rem;
          transition: all 0.3s ease;
          width: 100%;
      }
      
      .demo-button:hover {
          transform: translateY(-2px);
          box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
      }
      
      .demo-result {
          margin-top: 3rem;
          padding: 2.5rem;
          background: #f8f9ff;
          border-radius: 15px;
          display: none;
          border-left: 5px solid #667eea;
      }
      
      .testimonial-section {
          padding: 6rem 0;
          background: white;
      }
      
      .testimonial-card {
          max-width: 900px;
          margin: 0 auto;
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          color: white;
          padding: 4rem;
          border-radius: 25px;
          text-align: center;
          position: relative;
          overflow: hidden;
      }
      
      .testimonial-quote {
          font-size: 1.5rem;
          font-style: italic;
          margin-bottom: 2rem;
          line-height: 1.8;
          position: relative;
          z-index: 2;
      }
      
      .testimonial-author {
          font-size: 1.3rem;
          font-weight: 600;
          position: relative;
          z-index: 2;
      }
      
      .testimonial-role {
          font-size: 1rem;
          opacity: 0.9;
          position: relative;
          z-index: 2;
      }
      
      footer {
          background: #1a1a1a;
          color: white;
          padding: 5rem 0 2rem;
      }
      
      .footer-content {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 3rem;
          margin-bottom: 3rem;
      }
      
      .footer-section h4 {
          margin-bottom: 1.5rem;
          color: #667eea;
          font-size: 1.3rem;
          font-weight: 600;
      }
      
      .footer-links {
          list-style: none;
      }
      
      .footer-links li {
          margin-bottom: 0.8rem;
      }
      
      .footer-links a {
          color: #ccc;
          text-decoration: none;
          transition: color 0.3s ease;
      }
      
      .footer-links a:hover {
          color: #667eea;
      }
      
      .footer-bottom {
          border-top: 1px solid #333;
          padding-top: 2rem;
          text-align: center;
          color: #999;
      }
      
      @keyframes slideInDown {
          from {
              opacity: 0;
              transform: translateY(-50px);
          }
          to {
              opacity: 1;
              transform: translateY(0);
          }
      }
      
      @keyframes slideInUp {
          from {
              opacity: 0;
              transform: translateY(50px);
          }
          to {
              opacity: 1;
              transform: translateY(0);
          }
      }
      
      @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
      }
      
      @media (max-width: 768px) {
          .logo {
              font-size: 3rem;
              flex-direction: column;
              gap: 0.5rem;
          }
          
          .logo-icon {
              width: 70px;
              height: 70px;
              font-size: 2rem;
          }
          
          .tagline {
              font-size: 1.4rem;
          }
          
          .hero-stats {
              flex-direction: column;
              gap: 1rem;
          }
          
          .features-grid,
          .capabilities-grid,
          .demo-steps {
              grid-template-columns: 1fr;
          }
          
          .cta-buttons {
              flex-direction: column;
              align-items: center;
          }
          
          .section-title {
              font-size: 2.2rem;
          }
          
          .demo-interface {
              padding: 2rem;
          }
      }
  </style>
</head>
<body>
  <!-- Back to Home Button -->
  <div style="position: fixed; top: 20px; left: 20px; z-index: 1000;">
      <a href="index.html" style="display: inline-flex; align-items: center; padding: 10px 16px; background: rgba(255, 255, 255, 0.95); color: #333; text-decoration: none; border-radius: 8px; font-weight: 500; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2); transition: all 0.3s ease;">
          <svg style="width: 20px; height: 20px; margin-right: 8px;" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          返回首页
      </a>
  </div>

  <header>
      <div class="container">
          <div class="header-content">
              <div class="logo">
                  <div class="logo-icon">Γ</div>
                  Gamma AI
              </div>
              <p class="tagline">Revolutionizing Content Creation with AI-Powered Design</p>
              <p class="subtitle">Create stunning presentations, websites, and social media posts in minutes—no design experience required</p>
              <div class="hero-stats">
                  <div class="stat">
                      <span class="stat-number">3</span>
                      <span class="stat-label">Content Types</span>
                  </div>
                  <div class="stat">
                      <span class="stat-number">∞</span>
                      <span class="stat-label">Customization</span>
                  </div>
                  <div class="stat">
                      <span class="stat-number">⚡</span>
                      <span class="stat-label">Minutes to Create</span>
                  </div>
              </div>
          </div>
      </div>
  </header>

  <section class="cta-section">
      <div class="container">
          <h2 class="cta-title">Transform Your Ideas Into Stunning Content</h2>
          <p class="cta-subtitle">Join thousands of creators who are revolutionizing their workflow with Gamma AI</p>
          <div class="cta-buttons">
              <a href="https://gamma.app" target="_blank" class="cta-button cta-primary">
                  🚀 Start Creating Free
              </a>
              <a href="https://www.youtube.com/watch?v=KcbXKUR7-a0&t=64s" target="_blank" class="cta-button cta-secondary">
                  📺 Watch Full Tutorial
              </a>
          </div>
      </div>
  </section>

  <section class="video-section">
      <div class="container">
          <div class="video-container">
              <h2 class="section-title">Master Gamma AI in Minutes</h2>
              <p class="section-subtitle">Complete tutorial covering presentations, websites, and advanced features</p>
              
              <div class="presenter-intro">
                  <h3>🎥 Presented by Kevin</h3>
                  <p>Comprehensive walkthrough in partnership with Gamma AI, featuring real-world examples and pro tips</p>
              </div>
              
              <div class="video-wrapper">
                  <iframe 
                      src="https://www.youtube.com/embed/KcbXKUR7-a0?start=64" 
                      title="How to Use Gamma AI (Full Tutorial for Presentations, Websites & More)"
                      frameborder="0" 
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" 
                      allowfullscreen>
                  </iframe>
              </div>
              <a href="https://www.youtube.com/watch?v=KcbXKUR7-a0&t=64s" target="_blank" class="cta-button cta-secondary">
                  🔗 Open in YouTube
              </a>
          </div>
      </div>
  </section>

  <section class="features-section">
      <div class="container">
          <div class="section-header">
              <h2 class="section-title">Key Features That Set Gamma AI Apart</h2>
              <p class="section-subtitle">Discover the powerful capabilities that make content creation effortless</p>
          </div>
          
          <div class="features-grid">
              <div class="feature-card" onclick="animateCard(this)">
                  <span class="feature-number">1</span>
                  <h3 class="feature-title">Multiple Content Creation Options</h3>
                  <p class="feature-description">Three powerful ways to bring your ideas to life, each designed for different workflows and preferences.</p>
                  <ul class="feature-list">
                      <li>Paste Text: Transform notes and outlines into visual designs</li>
                      <li>Import Files/URLs: Enhance existing documents and presentations</li>
                      <li>Generate from Prompts: Create complete content from simple descriptions</li>
                  </ul>
              </div>
              
              <div class="feature-card" onclick="animateCard(this)">
                  <span class="feature-number">2</span>
                  <h3 class="feature-title">Versatile Output Types</h3>
                  <p class="feature-description">Create any type of content you need with consistent quality and professional design standards.</p>
                  <ul class="feature-list">
                      <li>Presentations: Professional slide decks with animations</li>
                      <li>Web Pages: Responsive websites and landing pages</li>
                      <li>Documents: Formatted reports and documents</li>
                      <li>Social Media Posts: Engaging visual content for all platforms</li>
                  </ul>
              </div>
              
              <div class="feature-card" onclick="animateCard(this)">
                  <span class="feature-number">3</span>
                  <h3 class="feature-title">Advanced Customization</h3>
                  <p class="feature-description">Fine-tune every aspect of your content with AI-powered customization options and manual controls.</p>
                  <ul class="feature-list">
                      <li>Themes: Choose from variety of professional themes</li>
                      <li>Text Density: Adjust content detail from brief to comprehensive</li>
                      <li>AI Visuals: Generate custom images with style control</li>
                      <li>Color Palettes: Match your brand or mood perfectly</li>
                  </ul>
              </div>
              
              <div class="feature-card" onclick="animateCard(this)">
                  <span class="feature-number">4</span>
                  <h3 class="feature-title">Interactive Editing</h3>
                  <p class="feature-description">Modify and refine your content with intuitive AI-powered editing tools and drag-and-drop simplicity.</p>
                  <ul class="feature-list">
                      <li>AI Text Modification: Smart content suggestions and improvements</li>
                      <li>Visual Editing: Modify AI-generated images and layouts</li>
                      <li>Drag-and-Drop: Rearrange slides and cards effortlessly</li>
                      <li>Template Addition: Add new sections with AI assistance</li>
                  </ul>
              </div>
              
              <div class="feature-card" onclick="animateCard(this)">
                  <span class="feature-number">5</span>
                  <h3 class="feature-title">Manual Control</h3>
                  <p class="feature-description">Take full control when needed with comprehensive manual editing tools and content blocks.</p>
                  <ul class="feature-list">
                      <li>Text Blocks: Rich text editing with formatting options</li>
                      <li>Media Integration: Images, videos, and interactive elements</li>
                      <li>Data Visualization: Tables, charts, and infographics</li>
                      <li>Interactive Elements: Buttons, links, and navigation</li>
                  </ul>
              </div>
          </div>
      </div>
  </section>

  <section class="demo-section">
      <div class="container">
          <div class="section-header">
              <h2 class="section-title">Kevin's Cookie Company Demo</h2>
              <p class="section-subtitle">Follow along as Kevin creates a professional pitch deck for his subscription box business</p>
          </div>
          
          <div class="demo-steps">
              <div class="demo-step">
                  <div class="demo-step-number">1</div>
                  <h3 class="demo-step-title">Generate Outline</h3>
                  <p class="demo-step-description">Kevin creates an 8-slide pitch deck outline for his monthly cookie subscription service using a simple prompt.</p>
              </div>
              
              <div class="demo-step">
                  <div class="demo-step-number">2</div>
                  <h3 class="demo-step-title">Customize Content</h3>
                  <p class="demo-step-description">Modify slide content, adjust visuals, and fine-tune the layout to match the brand vision and message.</p>
              </div>
              
              <div class="demo-step">
                  <div class="demo-step-number">3</div>
                  <h3 class="demo-step-title">Edit AI Images</h3>
                  <p class="demo-step-description">Use Gamma's AI image editor to modify, enhance, or completely regenerate visuals with different styles and compositions.</p>
              </div>

              <div class="demo-step">
                  <div class="demo-step-number">4</div>
                  <h3 class="demo-step-title">Export & Share</h3>
                  <p class="demo-step-description">Export your presentation in multiple formats or share directly with collaborators and audiences.</p>
              </div>
          </div>
      </div>
  </section>

  <section class="testimonial-section">
      <div class="container">
          <div class="section-header">
              <h2 class="section-title">What Users Say</h2>
              <p class="section-subtitle">Discover how Gamma AI is transforming content creation</p>
          </div>

          <div class="testimonial-card">
              <p class="testimonial-quote">
                  "Gamma AI has revolutionized how we create presentations. What used to take hours now takes minutes, and the quality is consistently professional."
              </p>
              <div class="testimonial-author">
                  <strong>Maria Rodriguez</strong><br>
                  <span>Marketing Director, TechCorp</span>
              </div>
          </div>
      </div>
  </section>

  <script>
      function generatePresentation() {
          const topic = document.getElementById('presentationTopic').value;
          const resultDiv = document.getElementById('presentationResult');

          if (!topic.trim()) {
              resultDiv.innerHTML = '<p style="color: #f5576c;">Please enter a presentation topic first!</p>';
              return;
          }

          const templates = [
              {
                  title: "Professional Business",
                  slides: ["Title Slide", "Problem Statement", "Solution Overview", "Key Benefits", "Implementation Plan", "Call to Action"],
                  style: "Clean, corporate design with blue accents"
              },
              {
                  title: "Creative Showcase",
                  slides: ["Creative Title", "Vision Statement", "Design Process", "Portfolio Highlights", "Client Testimonials", "Next Steps"],
                  style: "Vibrant colors with artistic elements"
              },
              {
                  title: "Educational Format",
                  slides: ["Learning Objectives", "Key Concepts", "Examples & Case Studies", "Interactive Elements", "Summary", "Resources"],
                  style: "Friendly, accessible design with illustrations"
              }
          ];

          const randomTemplate = templates[Math.floor(Math.random() * templates.length)];

          resultDiv.innerHTML = `
              <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 12px; margin-top: 15px;">
                  <h4 style="margin-bottom: 15px;">🎨 Generated Presentation: "${topic}"</h4>
                  <p><strong>Template:</strong> ${randomTemplate.title}</p>
                  <p><strong>Style:</strong> ${randomTemplate.style}</p>
                  <p><strong>Suggested Slides:</strong></p>
                  <ul style="margin-left: 20px; margin-top: 10px;">
                      ${randomTemplate.slides.map(slide => `<li>${slide}</li>`).join('')}
                  </ul>
                  <p style="margin-top: 15px; font-size: 14px; opacity: 0.9;">
                      ✨ AI will automatically generate content, select appropriate images, and apply consistent styling throughout your presentation.
                  </p>
              </div>
          `;
      }
  </script>

  <!-- Return to Index Link -->
  <div style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;">
      <a href="index.html" style="
          display: inline-block;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          text-decoration: none;
          padding: 12px 20px;
          border-radius: 50px;
          font-weight: 600;
          box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
          transition: all 0.3s ease;
          font-size: 14px;
      " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(102, 126, 234, 0.6)'"
         onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(102, 126, 234, 0.4)'">
          ← Back to Home
      </a>
  </div>

</body>
</html>