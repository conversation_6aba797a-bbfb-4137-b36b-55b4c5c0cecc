<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Tools for Education - Interactive Guide</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .instruction {
            background: #4285f4;
            color: white;
            padding: 15px 40px;
            text-align: center;
            font-style: italic;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            padding: 40px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            border-color: #4285f4;
        }
        
        .card.active {
            border-color: #4285f4;
            background: #f8f9ff;
        }
        
        .card-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            font-size: 2rem;
        }
        
        .card h3 {
            font-size: 1.4rem;
            margin-bottom: 15px;
            text-align: center;
            color: #333;
        }
        
        .card-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        
        .card.active .card-content {
            max-height: 1000px;
        }
        
        .card-details {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .card-details h4 {
            color: #4285f4;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .card-details p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .card-details ul {
            color: #666;
            padding-left: 20px;
            line-height: 1.6;
        }
        
        .card-details li {
            margin-bottom: 8px;
        }
        
        /* Card-specific colors */
        .card:nth-child(1) .card-icon { background: linear-gradient(135deg, #667eea, #764ba2); color: white; }
        .card:nth-child(2) .card-icon { background: linear-gradient(135deg, #f093fb, #f5576c); color: white; }
        .card:nth-child(3) .card-icon { background: linear-gradient(135deg, #4facfe, #00f2fe); color: white; }
        .card:nth-child(4) .card-icon { background: linear-gradient(135deg, #43e97b, #38f9d7); color: white; }
        .card:nth-child(5) .card-icon { background: linear-gradient(135deg, #fa709a, #fee140); color: white; }
        .card:nth-child(6) .card-icon { background: linear-gradient(135deg, #a8edea, #fed6e3); color: #333; }
        
        .footer {
            background: #f8f9fa;
            padding: 30px 40px;
            text-align: center;
            color: #666;
            border-top: 1px solid #eee;
        }
        
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: #4285f4;
            transition: width 0.3s ease;
            z-index: 1000;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
                padding: 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="progress-bar" id="progressBar"></div>
    
    <div class="container">
        <div class="header">
            <h1>AI Tools for Enhanced Learning</h1>
            <p>Discover how AI can revolutionize your educational experience with Google NotebookLM and Gemini. These powerful tools offer unique capabilities for saving time, personalizing learning, and sparking creativity.</p>
        </div>
        
        <div class="instruction">
            Click each card below to reveal more information and explore the possibilities!
        </div>
        
        <div class="grid">
            <div class="card" onclick="toggleCard(this)">
                <div class="card-icon">📚</div>
                <h3>Lesson Plans and Activities</h3>
                <div class="card-content">
                    <div class="card-details">
                        <h4>Transform Your Teaching Approach</h4>
                        <p>AI can revolutionize how you create and deliver educational content, making lesson planning more efficient and engaging.</p>
                        <h4>Key Features:</h4>
                        <ul>
                            <li>Generate comprehensive lesson plans tailored to specific learning objectives</li>
                            <li>Create interactive activities that adapt to different learning styles</li>
                            <li>Develop assessment rubrics and learning outcomes automatically</li>
                            <li>Design multimedia presentations with integrated content</li>
                        </ul>
                        <h4>Best Practices:</h4>
                        <p>Use NotebookLM to analyze your curriculum materials and generate structured lesson plans with verifiable citations. Combine with Gemini for creative activity ideas and innovative teaching approaches.</p>
                    </div>
                </div>
            </div>
            
            <div class="card" onclick="toggleCard(this)">
                <div class="card-icon">💬</div>
                <h3>Class Communications</h3>
                <div class="card-content">
                    <div class="card-details">
                        <h4>Streamline Student Engagement</h4>
                        <p>Enhance communication between educators and students with AI-powered tools that personalize interactions and improve clarity.</p>
                        <h4>Communication Tools:</h4>
                        <ul>
                            <li>Generate personalized feedback for student assignments</li>
                            <li>Create clear, engaging email communications</li>
                            <li>Develop FAQ responses based on common student questions</li>
                            <li>Design interactive discussion prompts and forums</li>
                        </ul>
                        <h4>Implementation Strategy:</h4>
                        <p>Use Gemini's Workspace integration to draft communications directly in Gmail and Google Docs, while leveraging NotebookLM to ensure all communications are grounded in course materials.</p>
                    </div>
                </div>
            </div>
            
            <div class="card" onclick="toggleCard(this)">
                <div class="card-icon">🎯</div>
                <h3>Relevant and Fun Materials</h3>
                <div class="card-content">
                    <div class="card-details">
                        <h4>Engage Students with Dynamic Content</h4>
                        <p>Create compelling educational materials that connect with students' interests and current events while maintaining academic rigor.</p>
                        <h4>Content Creation:</h4>
                        <ul>
                            <li>Generate real-world examples and case studies</li>
                            <li>Create multimedia content including images and videos</li>
                            <li>Develop interactive simulations and scenarios</li>
                            <li>Design gamified learning experiences</li>
                        </ul>
                        <h4>Personalization Approach:</h4>
                        <p>Use Gemini's broad knowledge base to find contemporary examples and cultural references, then validate and contextualize them using NotebookLM's source-grounded analysis.</p>
                    </div>
                </div>
            </div>
            
            <div class="card" onclick="toggleCard(this)">
                <div class="card-icon">✅</div>
                <h3>Assessment Assistant</h3>
                <div class="card-content">
                    <div class="card-details">
                        <h4>Revolutionize Student Evaluation</h4>
                        <p>Create fair, comprehensive, and efficient assessment tools that provide meaningful feedback and track learning progress.</p>
                        <h4>Assessment Features:</h4>
                        <ul>
                            <li>Generate diverse question types (multiple choice, essay, problem-solving)</li>
                            <li>Create rubrics aligned with learning objectives</li>
                            <li>Provide detailed, constructive feedback</li>
                            <li>Track student progress and identify learning gaps</li>
                        </ul>
                        <h4>Quality Assurance:</h4>
                        <p>Use NotebookLM to ensure all assessment questions are directly tied to course materials with proper citations, while Gemini can help generate creative question formats and scenarios.</p>
                    </div>
                </div>
            </div>
            
            <div class="card" onclick="toggleCard(this)">
                <div class="card-icon">📖</div>
                <h3>Reading Level Evaluation</h3>
                <div class="card-content">
                    <div class="card-details">
                        <h4>Optimize Content Accessibility</h4>
                        <p>Ensure your educational materials are appropriately challenging and accessible to all students by analyzing and adjusting reading complexity.</p>
                        <h4>Evaluation Tools:</h4>
                        <ul>
                            <li>Analyze text complexity and readability scores</li>
                            <li>Suggest vocabulary simplifications or enhancements</li>
                            <li>Create multiple versions for different reading levels</li>
                            <li>Identify potentially challenging concepts for pre-teaching</li>
                        </ul>
                        <h4>Adaptive Strategy:</h4>
                        <p>Upload course materials to NotebookLM for detailed analysis of reading levels, then use Gemini to generate alternative versions that maintain academic integrity while improving accessibility.</p>
                    </div>
                </div>
            </div>
            
            <div class="card" onclick="toggleCard(this)">
                <div class="card-icon">📊</div>
                <h3>Insightful Summaries</h3>
                <div class="card-content">
                    <div class="card-details">
                        <h4>Extract Key Learning Points</h4>
                        <p>Transform complex information into digestible insights that help students understand core concepts and connections.</p>
                        <h4>Summary Capabilities:</h4>
                        <ul>
                            <li>Generate comprehensive chapter and article summaries</li>
                            <li>Create visual mind maps and concept connections</li>
                            <li>Produce audio overviews for auditory learners</li>
                            <li>Identify key themes and recurring concepts</li>
                        </ul>
                        <h4>Multi-Modal Approach:</h4>
                        <p>NotebookLM excels at creating accurate, citation-backed summaries from your course materials, while its Audio Overview feature can generate engaging podcast-style discussions of key concepts.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>Remember:</strong> AI tools are most effective when used as collaborative partners in the learning process. Always maintain critical thinking and verify AI-generated content against authoritative sources.</p>
            <p style="margin-top: 10px; font-size: 0.9rem;">Based on "The Symbiotic Scholar: A Comprehensive Guide to Enhancing Student Learning with Google NotebookLM and Gemini"</p>
        </div>
    </div>
    
    <script>
        function toggleCard(card) {
            // Close all other cards
            const allCards = document.querySelectorAll('.card');
            allCards.forEach(c => {
                if (c !== card) {
                    c.classList.remove('active');
                }
            });
            
            // Toggle current card
            card.classList.toggle('active');
            
            // Update progress bar
            updateProgressBar();
            
            // Smooth scroll to card if on mobile
            if (window.innerWidth <= 768) {
                card.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
        
        function updateProgressBar() {
            const totalCards = document.querySelectorAll('.card').length;
            const activeCards = document.querySelectorAll('.card.active').length;
            const progress = (activeCards / totalCards) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
        }
        
        // Initialize progress bar
        updateProgressBar();
        
        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // Close all cards
                document.querySelectorAll('.card.active').forEach(card => {
                    card.classList.remove('active');
                });
                updateProgressBar();
            }
        });
        
        // Add smooth scrolling for better UX
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('click', function() {
                setTimeout(() => {
                    if (this.classList.contains('active')) {
                        this.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    }
                }, 100);
            });
        });
    </script>

    <!-- Return to Index Link -->
    <div style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;">
        <a href="index.html" style="
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 12px 20px;
            border-radius: 50px;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            font-size: 14px;
        " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(102, 126, 234, 0.6)'"
           onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(102, 126, 234, 0.4)'">
            ← Back to Home
        </a>
    </div>
</body>
</html>