<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canva 2025 Education Updates</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            scroll-behavior: smooth;
        }
        .tab-active {
            border-bottom-color: #8A3FFC;
            color: #8A3FFC;
            font-weight: 600;
        }
        .tab-content {
            display: none;
            animation: fadeIn 0.5s ease-in-out;
        }
        .tab-content.active {
            display: block;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .video-container {
            position: relative;
            padding-bottom: 56.25%; /* 16:9 */
            height: 0;
            overflow: hidden;
            max-width: 100%;
        }
        .video-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">

    <div class="container mx-auto p-4 md:p-8">
        <!-- Back to Home Button -->
        <div class="mb-6">
            <a href="index.html" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200 shadow-md hover:shadow-lg">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                返回首页
            </a>
        </div>

        <!-- Header Section -->
        <header class="text-center mb-8 md:mb-12">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-2">Canva Education Webinar Summary</h1>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">An interactive overview of the new tools and resources from Canva designed to enrich the classroom experience.</p>
        </header>

        <!-- YouTube Video Embed Section -->
        <div class="mb-8 md:mb-12">
            <h2 class="text-2xl md:text-3xl font-bold text-gray-900 text-center mb-6">Watch the Official Presentation</h2>
            <div class="video-container rounded-2xl shadow-xl mx-auto max-w-4xl bg-black">
                <iframe 
                    src="https://www.youtube.com/embed/Yq3iKsq86ZU?start=2568" 
                    title="YouTube video player" 
                    frameborder="0" 
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                    allowfullscreen>
                </iframe>
            </div>
        </div>

        <!-- Main Content with Tabs -->
        <div class="bg-white rounded-2xl shadow-xl p-6 md:p-8">
            <!-- Tabs Navigation -->
            <div class="border-b border-gray-200 mb-6">
                <nav class="flex flex-wrap -mb-px" aria-label="Tabs">
                    <button class="tab tab-active whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm md:text-base mr-4 md:mr-8" data-tab="suite">Visual Suite 2.0</button>
                    <button class="tab text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm md:text-base mr-4 md:mr-8" data-tab="sheets">Canva Sheets</button>
                    <button class="tab text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm md:text-base mr-4 md:mr-8" data-tab="ai">AI Tools</button>
                    <button class="tab text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm md:text-base mr-4 md:mr-8" data-tab="code">Canva Code</button>
                    <button class="tab text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm md:text-base" data-tab="school">Design School</button>
                </nav>
            </div>

            <!-- Content Panels -->
            <div>
                <!-- Visual Suite 2.0 Content -->
                <div id="suite-content" class="tab-content active">
                    <div class="grid md:grid-cols-2 gap-8 items-center">
                        <div class="prose max-w-none">
                            <h3 class="text-2xl font-semibold text-gray-900">Your All-in-One Creative Canvas</h3>
                            <p class="text-gray-600">Visual Suite 2.0 is a beacon of creativity, allowing you to craft comprehensive lesson plans and engaging student activities seamlessly within a single design. It transforms abstract concepts into colorful, interactive experiences.</p>
                             <p class="mt-4 text-purple-700 font-medium">With Visual Suite 2.0, lesson preparation becomes an inspiring journey, helping you captivate students' imaginations and foster a dynamic learning environment.</p>
                        </div>
                        <img src="https://placehold.co/600x400/8E44AD/FFFFFF?text=Visual+Suite+2.0" alt="Illustration of a digital canvas with educational elements" class="rounded-lg shadow-md w-full h-auto">
                    </div>
                </div>

                <!-- Canva Sheets Content -->
                <div id="sheets-content" class="tab-content">
                     <div class="grid md:grid-cols-2 gap-8 items-center">
                        <div class="prose max-w-none">
                            <h3 class="text-2xl font-semibold text-gray-900">Bring Data to Life</h3>
                            <p class="text-gray-600">Transform rigid, lifeless tables of data into vibrant, dynamic resources. Canva Sheets integrates features like <strong>Magic Charts</strong> and <strong>Magic Insights</strong> to illuminate trends and provide deeper understanding.</p>
                            <p class="mt-4 text-blue-700 font-medium">These tools empower you to manage student data with a finesse that turns analysis into an engaging educational narrative for personalized progress monitoring.</p>
                        </div>
                        <img src="https://placehold.co/600x400/3498DB/FFFFFF?text=Canva+Sheets" alt="Illustration of dynamic charts and graphs" class="rounded-lg shadow-md w-full h-auto">
                    </div>
                </div>

                <!-- AI Tools Content -->
                <div id="ai-content" class="tab-content">
                    <div class="grid md:grid-cols-2 gap-8 items-center">
                        <div class="prose max-w-none">
                            <h3 class="text-2xl font-semibold text-gray-900">Your Creative Teaching Assistant</h3>
                            <p class="text-gray-600">Canva's AI tools act as an artist's brush, helping you create tailored visuals and lesson plans with remarkable speed and ease. It's like having a skilled assistant ready to generate captivating graphics at the click of a button.</p>
                             <p class="mt-4 text-green-700 font-medium">This technology streamlines the creative process, allowing you to focus on what truly matters: inspiring students and igniting their passion for learning.</p>
                        </div>
                         <img src="https://placehold.co/600x400/2ECC71/FFFFFF?text=AI+Tools" alt="Illustration of AI generating educational visuals" class="rounded-lg shadow-md w-full h-auto">
                    </div>
                </div>
                
                <!-- Canva Code Content -->
                <div id="code-content" class="tab-content">
                    <div class="grid md:grid-cols-2 gap-8 items-center">
                        <div class="prose max-w-none">
                            <h3 class="text-2xl font-semibold text-gray-900">Learning Through Play</h3>
                            <p class="text-gray-600">The introduction of Canva Code opens a door to a world of interactive educational games, seamlessly blending technology with creativity. It's a powerful way for students to engage in learning through play.</p>
                            <p class="mt-4 text-orange-600 font-medium">Create custom games to tailor learning experiences to your curriculum, fostering an atmosphere of collaboration, critical thinking, and problem-solving.</p>
                        </div>
                         <img src="https://placehold.co/600x400/E67E22/FFFFFF?text=Canva+Code" alt="Illustration of a simple coding interface for games" class="rounded-lg shadow-md w-full h-auto">
                    </div>
                </div>

                <!-- Design School Content -->
                <div id="school-content" class="tab-content">
                    <div class="grid md:grid-cols-2 gap-8 items-center">
                        <div class="prose max-w-none">
                            <h3 class="text-2xl font-semibold text-gray-900">Continuous Professional Growth</h3>
                            <p class="text-gray-600">Canva's Design School is a treasure trove of free tutorials and resources. It offers a continuous learning journey, equipping you with the skills to refine and enhance your teaching practices.</p>
                            <p class="mt-4 text-red-600 font-medium">Just as a gardener nurtures plants, Design School cultivates your professional development, ensuring you remain at the forefront of innovative teaching strategies.</p>
                        </div>
                        <img src="https://placehold.co/600x400/E74C3C/FFFFFF?text=Design+School" alt="Illustration of online tutorials and learning resources" class="rounded-lg shadow-md w-full h-auto">
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="text-center mt-8 md:mt-12 text-gray-500 text-sm">
            <p>This page is an interactive summary of the Canva for Education webinar.</p>
        </footer>

    </div>

    <script>
        // JavaScript for tab functionality
        const tabs = document.querySelectorAll('.tab');
        const tabContents = document.querySelectorAll('.tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const target = tab.getAttribute('data-tab');

                // Deactivate all tabs and style them as inactive
                tabs.forEach(t => {
                    t.classList.remove('tab-active');
                    t.classList.add('text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
                });

                // Activate the clicked tab
                tab.classList.add('tab-active');
                tab.classList.remove('text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');

                // Hide all content panels
                tabContents.forEach(content => {
                    content.classList.remove('active');
                });

                // Show the target content panel
                const activeContent = document.getElementById(target + '-content');
                if (activeContent) {
                    activeContent.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
