<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Training Resources - Home</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            text-align: center;
            padding: 40px 20px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .intro {
            text-align: center;
            margin-bottom: 40px;
        }

        .intro h2 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.8rem;
        }

        .intro p {
            color: #718096;
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .resources-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .resource-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid #e2e8f0;
        }

        .resource-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .resource-card h3 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
        }

        .resource-card h3::before {
            content: "🤖";
            margin-right: 10px;
            font-size: 1.5rem;
        }

        .resource-card p {
            color: #4a5568;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .resource-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-align: center;
            width: 100%;
        }

        .resource-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .footer {
            background: #2d3748;
            color: white;
            text-align: center;
            padding: 30px;
        }

        .footer p {
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .resources-grid {
                grid-template-columns: 1fr;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>AI Training Resources</h1>
            <p>Your Gateway to AI Learning and Tools</p>
        </header>

        <main class="content">
            <section class="intro">
                <h2>Welcome to Your AI Learning Hub</h2>
                <p>Explore comprehensive guides and interactive resources for the latest AI tools and technologies. Each resource is designed to help you master cutting-edge AI applications.</p>
            </section>

            <section class="resources-grid">
                <div class="resource-card">
                    <h3>Canva 2025</h3>
                    <p><strong>Canva Sheet:</strong> Data visualization tools for creating stunning charts and infographics.<br>
                    <strong>Canva Code:</strong> Interactive pages, games, and dynamic content creation.</p>
                    <a href="Canva2025Education.html" class="resource-link">Explore Canva 2025 →</a>
                </div>

                <div class="resource-card">
                    <h3>Napkin AI</h3>
                    <p><strong>Diverse Choices for visualization:</strong> Transform your ideas into compelling visual stories with multiple visualization options and styles.</p>
                    <a href="NapkinAI.html" class="resource-link">Try Napkin AI →</a>
                </div>

                <div class="resource-card">
                    <h3>Gamma AI</h3>
                    <p><strong>AI Visuals:</strong> Generate custom images with style control.<br>
                    <strong>Quick to create presentation:</strong> Rapid presentation building.<br>
                    <strong>Note:</strong> Only supports 10 pages with free account.</p>
                    <a href="GammaAI.html" class="resource-link">Learn Gamma AI →</a>
                </div>

                <div class="resource-card">
                    <h3>Felo AI</h3>
                    <p><strong>AI search tools:</strong> Advanced search capabilities.<br>
                    <strong>Create presentation base templates:</strong> Template generation.<br>
                    <strong>Convert research result to Interactive Page:</strong> Transform research into engaging content.<br>
                    <strong>Text to Visual:</strong> Convert text into visual representations.</p>
                    <a href="FeloAI.html" class="resource-link">Discover Felo AI →</a>
                </div>

                <div class="resource-card">
                    <h3>Grok AI</h3>
                    <p><strong>Deeper search:</strong> Advanced search capabilities for comprehensive results.<br>
                    <strong>Image Tools:</strong> Create, edit, and modify images with AI.<br>
                    <strong>Schedule task:</strong> Task management and scheduling features.</p>
                    <a href="GrokAI.html" class="resource-link">Explore Grok AI →</a>
                </div>

                <div class="resource-card">
                    <h3>NotebookLM</h3>
                    <p><strong>Perfect for Every Learner:</strong> Adaptive learning experience tailored to individual needs.<br>
                    <strong>Audio Conversations:</strong> Interactive audio discussions and learning sessions.</p>
                    <a href="NotebookLM.html" class="resource-link">Learn NotebookLM →</a>
                </div>

                <div class="resource-card">
                    <h3>Google Gemini</h3>
                    <p><strong>More powerful than ChatGPT4-o:</strong> Advanced AI capabilities.<br>
                    <strong>Deep Research:</strong> Comprehensive research and analysis tools.<br>
                    <strong>Canvas:</strong> Creative workspace for visual projects.<br>
                    <strong>Create image:</strong> AI-powered image generation.</p>
                    <a href="Gemini2_5.html" class="resource-link">Explore Gemini →</a>
                </div>
            </section>
        </main>

        <footer class="footer">
            <p>&copy; 2025 AI Training Resources. Empowering learning through artificial intelligence.</p>
        </footer>
    </div>
</body>
</html>
