<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Napkin AI - Revolutionary Text-to-Graphics Tool</title>
  <style>
      * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
      }
      
      body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.6;
          color: #333;
          overflow-x: hidden;
      }
      
      .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 20px;
      }
      
      header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 2rem 0;
          text-align: center;
          position: relative;
          overflow: hidden;
      }
      
      header::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
          opacity: 0.3;
      }
      
      .header-content {
          position: relative;
          z-index: 1;
      }
      
      .logo {
          font-size: 3rem;
          font-weight: bold;
          margin-bottom: 1rem;
          animation: fadeInDown 1s ease;
      }
      
      .tagline {
          font-size: 1.3rem;
          opacity: 0.9;
          margin-bottom: 2rem;
          animation: fadeInUp 1s ease 0.2s both;
      }
      
      .video-section {
          background: #f8f9fa;
          padding: 4rem 0;
          text-align: center;
      }
      
      .video-container {
          max-width: 800px;
          margin: 0 auto;
          background: white;
          border-radius: 15px;
          padding: 2rem;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      }
      
      .video-embed {
          position: relative;
          width: 100%;
          height: 0;
          padding-bottom: 56.25%;
          border-radius: 10px;
          overflow: hidden;
          box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
      }
      
      .video-embed iframe {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
      }
      
      .video-link {
          display: inline-block;
          margin-top: 1rem;
          padding: 12px 25px;
          background: #ff6b6b;
          color: white;
          text-decoration: none;
          border-radius: 25px;
          font-weight: bold;
          transition: all 0.3s ease;
      }
      
      .video-link:hover {
          background: #ff5252;
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
      }
      
      .advantages {
          padding: 4rem 0;
          background: white;
      }
      
      .section-title {
          text-align: center;
          font-size: 2.5rem;
          margin-bottom: 1rem;
          color: #333;
          position: relative;
      }
      
      .section-subtitle {
          text-align: center;
          font-size: 1.1rem;
          color: #666;
          margin-bottom: 3rem;
      }
      
      .advantages-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
          gap: 2rem;
          margin-top: 2rem;
      }
      
      .advantage-card {
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          padding: 2rem;
          border-radius: 15px;
          position: relative;
          transition: all 0.3s ease;
          cursor: pointer;
          border: 2px solid transparent;
      }
      
      .advantage-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
          border-color: #667eea;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
      }
      
      .advantage-icon {
          font-size: 3rem;
          margin-bottom: 1rem;
          display: block;
      }
      
      .advantage-title {
          font-size: 1.4rem;
          font-weight: bold;
          margin-bottom: 1rem;
          color: #333;
      }
      
      .advantage-card:hover .advantage-title {
          color: white;
      }
      
      .advantage-description {
          font-size: 1rem;
          line-height: 1.6;
          color: #666;
      }
      
      .advantage-card:hover .advantage-description {
          color: rgba(255, 255, 255, 0.9);
      }
      
      .use-cases {
          background: #f8f9fa;
          padding: 4rem 0;
      }
      
      .use-cases-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 2rem;
      }
      
      .use-case {
          background: white;
          padding: 2rem;
          border-radius: 15px;
          text-align: center;
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
      }
      
      .use-case:hover {
          transform: translateY(-5px);
          box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
      }
      
      .use-case-icon {
          font-size: 2.5rem;
          margin-bottom: 1rem;
      }
      
      .cta-section {
          background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
          padding: 4rem 0;
          text-align: center;
          color: white;
      }
      
      .cta-button {
          display: inline-block;
          background: white;
          color: #ff6b6b;
          padding: 15px 30px;
          text-decoration: none;
          border-radius: 50px;
          font-weight: bold;
          font-size: 1.2rem;
          margin: 1rem;
          transition: all 0.3s ease;
      }
      
      .cta-button:hover {
          transform: translateY(-2px);
          box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
      }
      
      footer {
          background: #333;
          color: white;
          text-align: center;
          padding: 2rem 0;
      }
      
      @keyframes fadeInDown {
          from {
              opacity: 0;
              transform: translateY(-30px);
          }
          to {
              opacity: 1;
              transform: translateY(0);
          }
      }
      
      @keyframes fadeInUp {
          from {
              opacity: 0;
              transform: translateY(30px);
          }
          to {
              opacity: 1;
              transform: translateY(0);
          }
      }
      
      @media (max-width: 768px) {
          .advantages-grid {
              grid-template-columns: 1fr;
          }
          
          .logo {
              font-size: 2rem;
          }
          
          .section-title {
              font-size: 2rem;
          }
      }
  </style>
</head>
<body>
  <!-- Back to Home Button -->
  <div style="position: fixed; top: 20px; left: 20px; z-index: 1000;">
      <a href="index.html" style="display: inline-flex; align-items: center; padding: 10px 16px; background: rgba(255, 255, 255, 0.95); color: #333; text-decoration: none; border-radius: 8px; font-weight: 500; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2); transition: all 0.3s ease;">
          <svg style="width: 20px; height: 20px; margin-right: 8px;" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          返回首页
      </a>
  </div>

  <header>
      <div class="container">
          <div class="header-content">
              <div class="logo">🧠 Napkin AI</div>
              <div class="tagline">Transform Your Text into Stunning Visual Graphics Instantly</div>
          </div>
      </div>
  </header>

  <section class="video-section">
      <div class="container">
          <div class="video-container">
              <h2>Watch Napkin AI in Action</h2>
              <p style="margin-bottom: 2rem; color: #666;">See how easy it is to create professional graphics from simple text</p>
              <div class="video-embed">
                  <iframe src="https://www.youtube.com/embed/MzIVXbfK65k" 
                          title="Napkin AI Demo" 
                          frameborder="0" 
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                          allowfullscreen>
                  </iframe>
              </div>
              <a href="https://www.youtube.com/watch?v=MzIVXbfK65k" class="video-link" target="_blank">
                  🎥 Watch on YouTube
              </a>
          </div>
      </div>
  </section>

  <section class="advantages">
      <div class="container">
          <h2 class="section-title">7 Key Advantages of Napkin AI</h2>
          <p class="section-subtitle">Discover why Napkin AI is the ultimate tool for creating professional graphics</p>
          
          <div class="advantages-grid">
              <div class="advantage-card" onclick="highlightCard(this)">
                  <span class="advantage-icon">⚡</span>
                  <h3 class="advantage-title">Simple Operation</h3>
                  <p class="advantage-description">Users only need to input text and click a few buttons to quickly generate multiple graphic solutions. No complex design skills required - anyone can create professional visuals in minutes.</p>
              </div>
              
              <div class="advantage-card" onclick="highlightCard(this)">
                  <span class="advantage-icon">⏰</span>
                  <h3 class="advantage-title">Time-Saving</h3>
                  <p class="advantage-description">Rapidly generate graphics perfect for users who need efficient design output. Boost your work efficiency by eliminating hours of manual design work and focus on what matters most.</p>
              </div>
              
              <div class="advantage-card" onclick="highlightCard(this)">
                  <span class="advantage-icon">🎨</span>
                  <h3 class="advantage-title">Diverse Choices</h3>
                  <p class="advantage-description">Provides multiple styles and content graphic suggestions to meet different user needs and preferences. From minimalist to complex designs, find the perfect style for your content.</p>
              </div>
              
              <div class="advantage-card" onclick="highlightCard(this)">
                  <span class="advantage-icon">✏️</span>
                  <h3 class="advantage-title">Smart Editing</h3>
                  <p class="advantage-description">Generated graphics can be personalized and edited, allowing users to make adjustments based on specific needs. Fine-tune colors, layouts, and elements to match your vision perfectly.</p>
              </div>
              
              <div class="advantage-card" onclick="highlightCard(this)">
                  <span class="advantage-icon">🆓</span>
                  <h3 class="advantage-title">Completely Free</h3>
                  <p class="advantage-description">100% free to use, lowering the barrier to design and enabling more users to easily access high-quality graphics. No subscriptions, no hidden costs, no limitations.</p>
              </div>
              
              <div class="advantage-card" onclick="highlightCard(this)">
                  <span class="advantage-icon">🚀</span>
                  <h3 class="advantage-title">Multiple Use Cases</h3>
                  <p class="advantage-description">Suitable for various projects including business presentations, social media content, personal creative projects, and more. One tool for all your visual communication needs.</p>
              </div>
              
              <div class="advantage-card" onclick="highlightCard(this)">
                  <span class="advantage-icon">📊</span>
                  <h3 class="advantage-title">Content Quality Scoring</h3>
                  <p class="advantage-description">Combined with AI scoring tools to help users optimize content and enhance the final visual effect. Get insights on how to improve your graphics for maximum impact.</p>
              </div>
          </div>
      </div>
  </section>

  <section class="use-cases">
      <div class="container">
          <h2 class="section-title">Perfect for Every Project</h2>
          <div class="use-cases-grid">
              <div class="use-case">
                  <div class="use-case-icon">💼</div>
                  <h3>Business Presentations</h3>
                  <p>Create compelling slides and reports that capture attention and communicate effectively.</p>
              </div>
              <div class="use-case">
                  <div class="use-case-icon">📱</div>
                  <h3>Social Media</h3>
                  <p>Generate eye-catching posts, stories, and content that stands out in crowded feeds.</p>
              </div>
              <div class="use-case">
                  <div class="use-case-icon">🎨</div>
                  <h3>Personal Projects</h3>
                  <p>Bring your creative ideas to life with professional-quality graphics and designs.</p>
              </div>
              <div class="use-case">
                  <div class="use-case-icon">📚</div>
                  <h3>Educational Content</h3>
                  <p>Make learning materials more engaging with clear, informative visual aids.</p>
              </div>
              <div class="use-case">
                  <div class="use-case-icon">📊</div>
                  <h3>Data Visualization</h3>
                  <p>Transform complex data into easy-to-understand charts and infographics.</p>
              </div>
              <div class="use-case">
                  <div class="use-case-icon">🌐</div>
                  <h3>Web Content</h3>
                  <p>Enhance websites and blogs with relevant, high-quality visual elements.</p>
              </div>
          </div>
      </div>
  </section>

  <section class="cta-section">
      <div class="container">
          <h2>Ready to Transform Your Content?</h2>
          <p style="font-size: 1.2rem; margin-bottom: 2rem;">Join thousands of users who are already creating amazing graphics with Napkin AI</p>
          <a href="https://napkin.ai" class="cta-button" target="_blank">🚀 Start Creating Now</a>
          <a href="https://www.youtube.com/watch?v=MzIVXbfK65k" class="cta-button" target="_blank">📺 Watch Tutorial</a>
      </div>
  </section>

  <footer>
      <div class="container">
          <p>&copy; 2024 Napkin AI Guide. Revolutionizing graphic design with artificial intelligence.</p>
          <p style="margin-top: 0.5rem; opacity: 0.8;">Enhanced with AI quality optimization tools for superior results.</p>
      </div>
  </footer>

  <script>
      function highlightCard(card) {
          // Remove highlight from all cards
          document.querySelectorAll('.advantage-card').forEach(c => {
              c.style.transform = '';
              c.style.boxShadow = '';
          });
          
          // Highlight clicked card
          card.style.transform = 'translateY(-10px) scale(1.02)';
          card.style.boxShadow = '0 20px 40px rgba(102, 126, 234, 0.3)';
          
          setTimeout(() => {
              card.style.transform = '';
              card.style.boxShadow = '';
          }, 2000);
      }
      
      // Intersection Observer for animations
      const observerOptions = {
          threshold: 0.1,
          rootMargin: '0px 0px -50px 0px'
      };
      
      const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
              if (entry.isIntersecting) {
                  entry.target.style.animation = 'fadeInUp 0.6s ease forwards';
              }
          });
      }, observerOptions);
      
      // Observe elements for animation
      document.querySelectorAll('.advantage-card, .use-case').forEach(element => {
          observer.observe(element);
      });
      
      // Smooth scrolling for anchor links
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
          anchor.addEventListener('click', function (e) {
              e.preventDefault();
              const target = document.querySelector(this.getAttribute('href'));
              if (target) {
                  target.scrollIntoView({
                      behavior: 'smooth',
                      block: 'start'
                  });
              }
          });
      });
  </script>
</body>
</html>