<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Differentiation & Personalized Instruction - Interactive Guide</title>
  <style>
      * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
      }
      
      body {
          font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
          line-height: 1.6;
          color: #202124;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
      }
      
      .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 2rem;
      }
      
      header {
          text-align: center;
          color: white;
          margin-bottom: 3rem;
      }
      
      .main-title {
          font-size: 3rem;
          font-weight: 700;
          margin-bottom: 1rem;
          text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
      }
      
      .subtitle {
          font-size: 1.3rem;
          opacity: 0.9;
          max-width: 800px;
          margin: 0 auto 2rem;
      }
      
      .intro-text {
          background: rgba(255, 255, 255, 0.1);
          padding: 2rem;
          border-radius: 15px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          margin-bottom: 3rem;
          font-size: 1.1rem;
          line-height: 1.8;
      }
      
      .differentiation-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
          gap: 2rem;
          margin-bottom: 3rem;
      }
      
      .diff-card {
          background: white;
          border-radius: 20px;
          overflow: hidden;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
          transition: all 0.3s ease;
          cursor: pointer;
          position: relative;
      }
      
      .diff-card:hover {
          transform: translateY(-10px);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      }
      
      .diff-icon {
          height: 200px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 4rem;
          color: white;
          position: relative;
          overflow: hidden;
      }
      
      .diff-icon::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, #4285f4 0%, #ea4335 25%, #fbbc04 50%, #34a853 75%, #9aa0a6 100%);
          opacity: 0.9;
      }
      
      .diff-icon span {
          position: relative;
          z-index: 2;
      }
      
      .diff-content {
          padding: 2rem;
      }
      
      .diff-title {
          font-size: 1.5rem;
          font-weight: 700;
          margin-bottom: 1rem;
          color: #333;
      }
      
      .diff-description {
          color: #666;
          margin-bottom: 1.5rem;
          line-height: 1.6;
      }
      
      .try-button {
          background: linear-gradient(135deg, #667eea, #764ba2);
          color: white;
          border: none;
          padding: 0.8rem 1.5rem;
          border-radius: 25px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 0.9rem;
      }
      
      .try-button:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
      }
      
      .modal {
          display: none;
          position: fixed;
          z-index: 1000;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.8);
          backdrop-filter: blur(5px);
      }
      
      .modal-content {
          background-color: white;
          margin: 2% auto;
          padding: 0;
          border-radius: 20px;
          width: 90%;
          max-width: 800px;
          max-height: 90vh;
          overflow-y: auto;
          position: relative;
          animation: modalSlideIn 0.3s ease;
      }
      
      @keyframes modalSlideIn {
          from {
              opacity: 0;
              transform: translateY(-50px);
          }
          to {
              opacity: 1;
              transform: translateY(0);
          }
      }
      
      .modal-header {
          background: linear-gradient(135deg, #667eea, #764ba2);
          color: white;
          padding: 2rem;
          border-radius: 20px 20px 0 0;
      }
      
      .modal-title {
          font-size: 2rem;
          font-weight: 700;
          margin-bottom: 0.5rem;
      }
      
      .modal-body {
          padding: 2rem;
      }
      
      .close {
          position: absolute;
          right: 1.5rem;
          top: 1.5rem;
          color: white;
          font-size: 2rem;
          font-weight: bold;
          cursor: pointer;
          transition: all 0.3s ease;
      }
      
      .close:hover {
          transform: scale(1.1);
          opacity: 0.8;
      }
      
      .prompt-section {
          background: #f8f9fa;
          padding: 1.5rem;
          border-radius: 10px;
          margin: 1.5rem 0;
          border-left: 4px solid #667eea;
      }
      
      .prompt-title {
          font-weight: 700;
          margin-bottom: 1rem;
          color: #333;
      }
      
      .prompt-text {
          background: white;
          padding: 1rem;
          border-radius: 8px;
          border: 1px solid #e9ecef;
          font-family: 'Courier New', monospace;
          font-size: 0.9rem;
          line-height: 1.5;
          user-select: all;
      }
      
      .prompt-field {
          background: #e3f2fd;
          color: #1565c0;
          padding: 2px 6px;
          border-radius: 4px;
          font-weight: 500;
      }
      
      .select-tip {
          background: #e8f5e8;
          color: #2e7d32;
          padding: 0.5rem;
          border-radius: 5px;
          margin-top: 1rem;
          font-size: 0.9rem;
          font-style: italic;
      }
      
      .google-footer {
          background: rgba(255, 255, 255, 0.1);
          color: white;
          padding: 2rem;
          text-align: center;
          border-radius: 15px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
      }
      
      @media (max-width: 768px) {
          .main-title {
              font-size: 2rem;
          }
          
          .differentiation-grid {
              grid-template-columns: 1fr;
          }
          
          .modal-content {
              width: 95%;
              margin: 5% auto;
          }
      }
  </style>
</head>
<body>
  <div class="container">
      <header>
          <h1 class="main-title">🎯 AI Differentiation & Personalized Instruction</h1>
          <p class="subtitle">Customize learning experiences to meet every student's unique needs and abilities</p>
          <div class="intro-text">
              Earlier in this course, you used an AI tool to help with common activities, including planning a trip and creating an icebreaker. Now, click on each image to reveal even more tactics for saving you time, personalizing student learning, and sparking creativity.
              <br><br><strong>Click each differentiation strategy to reveal detailed prompts and examples!</strong>
          </div>
      </header>

      <div class="differentiation-grid">
          <div class="diff-card" onclick="openModal('fastFinishers')">
              <div class="diff-icon">
                  <span>⚡</span>
              </div>
              <div class="diff-content">
                  <h3 class="diff-title">Add Differentiation for Fast Finishers</h3>
                  <p class="diff-description">Create extension activities and enrichment opportunities for students who complete tasks quickly, keeping them engaged and challenged.</p>
                  <button class="try-button">Try It Out →</button>
              </div>
          </div>

          <div class="diff-card" onclick="openModal('masteryLevels')">
              <div class="diff-icon">
                  <span>📊</span>
              </div>
              <div class="diff-content">
                  <h3 class="diff-title">Create Tasks for Different Mastery Levels</h3>
                  <p class="diff-description">Design project tasks that accommodate students with varying levels of mastery and experience in the subject matter.</p>
                  <button class="try-button">Try It Out →</button>
              </div>
          </div>

          <div class="diff-card" onclick="openModal('readingLevel')">
              <div class="diff-icon">
                  <span>📖</span>
              </div>
              <div class="diff-content">
                  <h3 class="diff-title">Check & Adjust Reading Level</h3>
                  <p class="diff-description">Evaluate and modify text complexity to ensure materials are accessible and appropriate for your students' reading abilities.</p>
                  <button class="try-button">Try It Out →</button>
              </div>
          </div>

          <div class="diff-card" onclick="openModal('summarizeContent')">
              <div class="diff-icon">
                  <span>📝</span>
              </div>
              <div class="diff-content">
                  <h3 class="diff-title">Summarize Content</h3>
                  <p class="diff-description">Create clear, concise summaries of lesson content for absent students or those who need additional support.</p>
                  <button class="try-button">Try It Out →</button>
              </div>
          </div>
      </div>

      <div class="google-footer">
          <p><strong>Google for Education AI Prompt Library</strong></p>
          <p>© 2024 Google LLC 1600 Amphitheatre Parkway, Mountain View, CA 94043</p>
      </div>
  </div>

  <!-- Modals -->
  <div id="fastFinishersModal" class="modal">
      <div class="modal-content">
          <div class="modal-header">
              <span class="close" onclick="closeModal('fastFinishersModal')">&times;</span>
              <h2 class="modal-title">⚡ Add Differentiation for Fast Finishers</h2>
          </div>
          <div class="modal-body">
              <p>Create extension activities and enrichment opportunities for students who complete tasks quickly, keeping them engaged and challenged while others finish their work.</p>
              
              <div class="prompt-section">
                  <h3 class="prompt-title">Differentiation for Fast Finishers Template:</h3>
                  <div class="prompt-text">
I am a <span class="prompt-field">[Enter your role]</span>. Based on the following information, suggest ways to add differentiation to the activity for students who finish quickly:

Activity: <span class="prompt-field">[Enter activity name]</span>
Grade level: <span class="prompt-field">[Enter grade level]</span>
Learning objectives: 
● <span class="prompt-field">[Enter learning objective]</span>
● <span class="prompt-field">[Enter learning objective]</span>
● <span class="prompt-field">[Enter learning objective]</span>

Clear instructions and expectations: <span class="prompt-field">[Explain how the activity will be conducted and what students should accomplish]</span>
Resources: <span class="prompt-field">[List required resources]</span>
Constraints: <span class="prompt-field">[Note any potential limitations]</span>
Specific theme or context: <span class="prompt-field">[Include information about the activity's theme or the context in which it will exist]</span>
                  </div>
                  <div class="select-tip">💡 Tip: Select all the text above to copy it to your clipboard</div>
              </div>
          </div>
      </div>
  </div>

  <div id="masteryLevelsModal" class="modal">
      <div class="modal-content">
          <div class="modal-header">
              <span class="close" onclick="closeModal('masteryLevelsModal')">&times;</span>
              <h2 class="modal-title">📊 Create Tasks for Different Mastery Levels</h2>
          </div>
          <div class="modal-body">
              <p>Design project tasks that accommodate students with varying levels of mastery and experience, ensuring appropriate challenge and support for all learners.</p>
              
              <div class="prompt-section">
                  <h3 class="prompt-title">Detailed Mastery Level Template:</h3>
                  <div class="prompt-text">
I am a <span class="prompt-field">[Enter your role]</span>. Based on the following information, suggest ways to differentiate project tasks for students with different levels of mastery and experience:

Project: <span class="prompt-field">[Enter project name]</span>
Grade level: <span class="prompt-field">[Enter grade level]</span>
Learning objectives: 
● <span class="prompt-field">[Enter learning objective]</span>
● <span class="prompt-field">[Enter learning objective]</span>
● <span class="prompt-field">[Enter learning objective]</span>

Clear instructions and expectations: <span class="prompt-field">[Explain how the activity will be conducted and what students should accomplish]</span>
Resources: <span class="prompt-field">[List required resources]</span>
Constraints: <span class="prompt-field">[Note any potential limitations]</span>
Specific theme or context: <span class="prompt-field">[Include information about the project's theme or the context in which it will exist]</span>
                  </div>
                  <div class="select-tip">💡 Tip: Select all the text above to copy it to your clipboard</div>
              </div>

              <div class="prompt-section">
                  <h3 class="prompt-title">Quick Version:</h3>
                  <div class="prompt-text">
I am a <span class="prompt-field">[Enter your role]</span>. Suggest ways to differentiate project tasks for students with different levels of mastery and experience with <span class="prompt-field">[Enter subject]</span>. They must achieve <span class="prompt-field">[Enter project goals]</span> by the end of the project.
                  </div>
                  <div class="select-tip">💡 Tip: Select all the text above to copy it to your clipboard</div>
              </div>
          </div>
      </div>
  </div>

  <div id="readingLevelModal" class="modal">
      <div class="modal-content">
          <div class="modal-header">
              <span class="close" onclick="closeModal('readingLevelModal')">&times;</span>
              <h2 class="modal-title">📖 Check & Adjust Reading Level</h2>
          </div>
          <div class="modal-body">
              <p>Evaluate and modify text complexity to ensure materials are accessible and appropriate for your students' reading abilities and comprehension levels.</p>
              
              <div class="prompt-section">
                  <h3 class="prompt-title">Check Reading Level:</h3>
                  <div class="prompt-text">
I am a <span class="prompt-field">[Enter your role]</span>. Review this <span class="prompt-field">[Enter assignment]</span> and point out areas where the writing can be simplified in order to ensure the reading level is appropriate for my <span class="prompt-field">[Enter grade level]</span> students: <span class="prompt-field">[Enter assignment]</span>
                  </div>
                  <div class="select-tip">💡 Tip: Select all the text above to copy it to your clipboard</div>
              </div>

              <div class="prompt-section">
                  <h3 class="prompt-title">Adjust Reading Level - Detailed:</h3>
                  <div class="prompt-text">
I am a <span class="prompt-field">[Enter your role]</span>. Adjust the reading level of the following text to make it more accessible for students at a <span class="prompt-field">[Target reading level]</span> level.

Original text: <span class="prompt-field">[Paste in the text]</span>
Subject: <span class="prompt-field">[Note the subject of the text]</span>
Topic: <span class="prompt-field">[Specify the specific topic covered in the text]</span>
Target audience: <span class="prompt-field">[Describe the students who will be reading the adjusted text, including their age, grade level, and any relevant learning needs]</span>
Key points to preserve: <span class="prompt-field">[List the essential information and main points that must be retained in the adjusted text]</span>
Vocabulary: <span class="prompt-field">[Include any complex vocabulary or jargon that needs to be simplified or explained]</span>
Sentence structure: <span class="prompt-field">[Reference any desired changes to sentence length or complexity]</span>
Additional notes: <span class="prompt-field">[Add any other helpful information or preferences for the adjusted text, such as tone, format, or specific areas to focus on]</span>
                  </div>
                  <div class="select-tip">💡 Tip: Select all the text above to copy it to your clipboard</div>
              </div>

              <div class="prompt-section">
                  <h3 class="prompt-title">Adjust Reading Level - Simple:</h3>
                  <div class="prompt-text">
I am a <span class="prompt-field">[Enter your role]</span>. Adjust the reading level of the following text to make it more accessible for students at a <span class="prompt-field">[Target reading level]</span> level. <span class="prompt-field">[Paste in text.]</span>
                  </div>
                  <div class="select-tip">💡 Tip: Select all the text above to copy it to your clipboard</div>
              </div>
          </div>
      </div>
  </div>

  <div id="summarizeContentModal" class="modal">
      <div class="modal-content">
          <div class="modal-header">
              <span class="close" onclick="closeModal('summarizeContentModal')">&times;</span>
              <h2 class="modal-title">📝 Summarize Content</h2>
          </div>
          <div class="modal-body">
              <p>Create clear, concise summaries of lesson content for absent students or those who need additional support understanding the material.</p>
              
              <div class="prompt-section">
                  <h3 class="prompt-title">Comprehensive Summary Template:</h3>
                  <div class="prompt-text">
I am a <span class="prompt-field">[Enter your role]</span>. Summarize the following content for any students who are not present during the classroom discussion. Ensure the summary is clear, concise, and easy to understand:

Subject and topic: <span class="prompt-field">[State the subject and specific topic covered in the lesson]</span>
Reading or excerpt: <span class="prompt-field">[If appropriate, enter reading or excerpt name, or paste in the content]</span>
Reading level: <span class="prompt-field">[Note the desired reading level]</span>
Learning objectives: 
<span class="prompt-field">[Enter learning objective]</span>
<span class="prompt-field">[Enter learning objective]</span>
<span class="prompt-field">[Enter learning objective]</span>

Key points and details: <span class="prompt-field">[Summarize the main points and arguments]</span>
Terminology: <span class="prompt-field">[Define or explain any important terms or concepts used in the lesson]</span>
Format: <span class="prompt-field">[Specify the desired format, such as bullet points, a timeline, a comparison chart, etc.]</span>
Specific theme or context: <span class="prompt-field">[Include information about the content's theme or the context in which it will exist]</span>
Level of detail: <span class="prompt-field">[Adjust the level of detail to be appropriate for your objective]</span>
                  </div>
                  <div class="select-tip">💡 Tip: Select all the text above to copy it to your clipboard</div>
              </div>

              <div class="prompt-section">
                  <h3 class="prompt-title">Quick Summary Version:</h3>
                  <div class="prompt-text">
I am a <span class="prompt-field">[Enter your role]</span> teaching a lesson about <span class="prompt-field">[State the subject and specific topic covered in the lesson]</span>. Summarize the following content for any students who are not present during the classroom discussion: <span class="prompt-field">[Enter reading or excerpt name, or paste in the content]</span>. The reading level is <span class="prompt-field">[Note the desired reading level]</span>.
                  </div>
                  <div class="select-tip">💡 Tip: Select all the text above to copy it to your clipboard</div>
              </div>
          </div>
      </div>
  </div>

  <script>
      function openModal(modalId) {
          const modal = document.getElementById(modalId + 'Modal');
          modal.style.display = 'block';
          document.body.style.overflow = 'hidden';
      }

      function closeModal(modalId) {
          const modal = document.getElementById(modalId);
          modal.style.display = 'none';
          document.body.style.overflow = 'auto';
      }

      // Close modal when clicking outside of it
      window.onclick = function(event) {
          const modals = document.querySelectorAll('.modal');
          modals.forEach(modal => {
              if (event.target === modal) {
                  modal.style.display = 'none';
                  document.body.style.overflow = 'auto';
              }
          });
      }

      // Close modal with Escape key
      document.addEventListener('keydown', function(event) {
          if (event.key === 'Escape') {
              const modals = document.querySelectorAll('.modal');
              modals.forEach(modal => {
                  if (modal.style.display === 'block') {
                      modal.style.display = 'none';
                      document.body.style.overflow = 'auto';
                  }
              });
          }
      });
  </script>
</body>
</html>