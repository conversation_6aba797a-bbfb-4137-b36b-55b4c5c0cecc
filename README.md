# AI Training Resources 🤖

Your comprehensive gateway to AI learning and tools - featuring interactive guides and resources for the latest AI technologies.

## 🌟 Overview

This repository contains interactive HTML guides and educational resources for mastering cutting-edge AI applications. Each resource is designed to provide hands-on learning experiences with detailed explanations and practical examples.

## 🚀 Featured AI Tools

### 1. **Canva 2025** 🎨
- **Canva Sheet**: Advanced data visualization tools for creating stunning charts and infographics
- **Canva Code**: Interactive pages, games, and dynamic content creation capabilities
- [📖 Learn More](Canva2025Education.html)

### 2. **Napkin AI** 📊
- **Diverse Choices for Visualization**: Transform your ideas into compelling visual stories with multiple visualization options and styles
- Perfect for creating professional diagrams and visual content
- [📖 Learn More](NapkinAI.html)

### 3. **Gamma AI** ✨
- **AI Visuals**: Generate custom images with advanced style control
- **Quick Presentation Creation**: Rapid presentation building with AI assistance
- **Note**: Free account supports up to 10 pages
- [📖 Learn More](GammaAI.html)

### 4. **Felo AI** 🔍
- **AI Search Tools**: Advanced search capabilities for comprehensive research
- **Presentation Base Templates**: Automated template generation
- **Research to Interactive Pages**: Convert research results into engaging interactive content
- **Text to Visual**: Transform text content into visual representations
- [📖 Learn More](FeloAI.html)

### 5. **Grok AI** 🧠
- **Deeper Search**: Advanced search capabilities for comprehensive results
- **Image Tools**: Create, edit, and modify images with AI assistance
- **Schedule Tasks**: Built-in task management and scheduling features
- [📖 Learn More](GrokAI.html)

### 6. **NotebookLM** 📚
- **Perfect for Every Learner**: Adaptive learning experience tailored to individual needs
- **Audio Conversations**: Interactive audio discussions and learning sessions
- Google's AI-powered research and note-taking tool
- [📖 Learn More](NotebookLM.html)

### 7. **Google Gemini** 🌟
- **More Powerful than ChatGPT-4o**: Advanced AI capabilities and performance
- **Deep Research**: Comprehensive research and analysis tools
- **Canvas**: Creative workspace for visual projects and collaboration
- **Image Creation**: AI-powered image generation and editing
- [📖 Learn More](Gemini2_5.html)

## 🎯 Getting Started

1. **Clone the repository**:
   ```bash
   git clone https://github.com/Alliu60/Training.git
   cd Training
   ```

2. **Open the main page**:
   - Open `index.html` in your web browser
   - Navigate through the interactive guides
   - Explore each AI tool's capabilities

3. **Browse individual guides**:
   - Each HTML file contains comprehensive tutorials
   - Interactive examples and practical applications
   - Step-by-step learning paths

## 📁 Repository Structure

```
Training/
├── index.html              # Main landing page with all tool links
├── Canva2025Education.html # Canva 2025 comprehensive guide
├── NapkinAI.html          # Napkin AI visualization guide
├── GammaAI.html           # Gamma AI content creation guide
├── FeloAI.html            # Felo AI research assistant guide
├── GrokAI.html            # Grok AI advanced features guide
├── NotebookLM.html        # NotebookLM learning guide
├── Gemini2_5.html         # Google Gemini complete guide
├── README.md              # This file
├── sitemap.xml            # Site structure for search engines
└── BingSiteAuth.xml       # Bing webmaster verification
```

## 🌐 Live Demo

Visit the live version: [AI Training Resources](https://alliu60.github.io/Training/)

## 🤝 Contributing

Contributions are welcome! If you'd like to add new AI tools or improve existing guides:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/new-ai-tool`)
3. Commit your changes (`git commit -am 'Add new AI tool guide'`)
4. Push to the branch (`git push origin feature/new-ai-tool`)
5. Create a Pull Request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 📞 Contact

- **Author**: Mosiacfan
- **GitHub**: [@Alliu60](https://github.com/Alliu60)
- **Email**: <EMAIL>

## 🔄 Updates

This repository is regularly updated with new AI tools and enhanced guides. Star ⭐ the repository to stay updated with the latest AI learning resources!

---

*Empowering learning through artificial intelligence* 🚀